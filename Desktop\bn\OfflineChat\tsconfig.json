{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@features/*": ["src/features/*"], "@services/*": ["src/services/*"], "@utils/*": ["src/utils/*"], "@types/*": ["src/types/*"], "@constants/*": ["src/constants/*"], "@hooks/*": ["src/hooks/*"], "@navigation/*": ["src/navigation/*"], "@store/*": ["src/store/*"], "@api/*": ["src/api/*"], "@lib/*": ["src/lib/*"], "@context/*": ["src/context/*"], "@assets/*": ["assets/*"]}, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules"]}