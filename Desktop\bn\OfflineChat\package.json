{"name": "offlinechat", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"expo": "~53.0.20", "expo-status-bar": "~2.2.3", "expo-router": "~4.0.9", "expo-sqlite": "~14.0.6", "expo-crypto": "~13.0.2", "expo-network": "~6.0.1", "expo-permissions": "~14.4.0", "expo-location": "~17.0.1", "expo-secure-store": "~13.0.2", "react": "19.0.0", "react-native": "0.79.5", "react-native-tcp-socket": "^6.0.6", "react-native-udp": "^4.1.5", "react-native-wifi-reborn": "^4.12.0", "react-native-network-info": "^5.2.1", "@reduxjs/toolkit": "^2.0.1", "react-redux": "^9.0.4", "react-native-vector-icons": "^10.0.3", "react-native-paper": "^5.12.3", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "^3.29.0", "react-native-gesture-handler": "^2.14.1", "react-native-reanimated": "^3.6.2", "react-native-svg": "^14.1.0", "lottie-react-native": "^6.4.1", "react-native-keychain": "^8.1.3", "react-native-device-info": "^10.13.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "@types/jest": "^29.5.12", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-native": "^4.1.0", "jest": "^29.7.0", "jest-expo": "^51.0.4", "prettier": "^3.2.5", "typescript": "~5.8.3"}, "jest": {"preset": "jest-expo", "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"]}, "private": true}