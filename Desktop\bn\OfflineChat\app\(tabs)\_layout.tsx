// Tab layout for main app navigation

import React from 'react';
import { Tabs } from 'expo-router';
import { Colors, Typography } from '@constants/theme';

export default function TabLayout() {
  const isDarkMode = false; // This would come from theme context
  const colors = isDarkMode ? Colors.dark : Colors.light;

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarStyle: {
          backgroundColor: colors.card,
          borderTopColor: colors.border,
          borderTopWidth: 1,
        },
        tabBarLabelStyle: {
          fontSize: Typography.fontSize.xs,
          fontWeight: Typography.fontWeight.medium,
        },
        headerStyle: {
          backgroundColor: colors.background,
          borderBottomColor: colors.border,
          borderBottomWidth: 1,
        },
        headerTitleStyle: {
          color: colors.text,
          fontSize: Typography.fontSize.lg,
          fontWeight: Typography.fontWeight.semibold,
        },
      }}
    >
      <Tabs.Screen
        name="discover"
        options={{
          title: 'Discover',
          tabBarIcon: ({ color, size }) => (
            // In a real app, use proper icons
            <span style={{ fontSize: size, color }}>🔍</span>
          ),
        }}
      />
      <Tabs.Screen
        name="chat"
        options={{
          title: 'Chat',
          tabBarIcon: ({ color, size }) => (
            <span style={{ fontSize: size, color }}>💬</span>
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color, size }) => (
            <span style={{ fontSize: size, color }}>👤</span>
          ),
        }}
      />
    </Tabs>
  );
}
